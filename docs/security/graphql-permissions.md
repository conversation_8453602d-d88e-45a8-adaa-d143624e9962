# GraphQL权限控制系统设计文档

## 概述

本文档描述了Firespoon API的GraphQL权限控制系统的重新设计。该系统基于对实际架构的深入理解，简化了权限架构，明确区分了不同的访问场景，提供了更加精确和高效的安全控制。

## 架构理解与设计原则

### 🎯 核心理解

经过深入分析，我们发现了一个关键的架构事实：

**WhatsApp系统调用不经过HTTP GraphQL端点**

```
真实的消息流程：
WhatsApp用户 → Payemoji → Webhook(X-MMC-signature) → 服务器内部处理 → 直接调用GraphQL resolvers
```

这意味着：
- WhatsApp系统的调用是服务器内部的直接函数调用
- 这些调用不会触发GraphQL权限规则
- 对WhatsApp系统进行GraphQL权限限制是无效的

### 🔐 重新设计的认证类型

基于上述理解，系统简化为以下认证机制：

1. **JWT认证** (`AUTH_TYPES.JWT`)
   - 标准的Bearer Token认证
   - 用于普通用户、餐厅用户、管理员等
   - 适用于所有标准的GraphQL HTTP请求

2. **X-WhatsAppW-Token认证** (`AUTH_TYPES.WEB_WHATSAPP`)
   - 使用 `X-WhatsAppW-Token` 请求头
   - **专门用于web页面的受限访问**
   - 严格限制只能访问特定操作：
     - `getCustomerAddresses` - 查询用户地址
     - `placeOrderFromWhatsApp` - 提交订单

### ⚠️ 重要架构区分

**三种不同的访问模式**：

1. **Webhook认证** (X-MMC-signature)
   - 验证来自Payemoji的webhook请求
   - 不涉及GraphQL权限控制
   - 通过HMAC签名验证

2. **内部调用**
   - 服务器内部直接调用GraphQL resolvers
   - 通过customerId参数标识
   - 绕过所有HTTP权限检查

3. **Web页面请求** (X-WhatsAppW-Token)
   - 来自web页面的HTTP GraphQL请求
   - 需要严格的权限限制
   - 只能访问预定义的安全操作

## 权限规则设计

### 🛡️ 核心权限规则

#### 基础认证规则
- `isAuthenticated` - 检查JWT认证
  - 验证Bearer Token的有效性
  - 适用于所有需要用户身份验证的操作

#### 特殊认证规则（重新设计）
- `isWebWhatsAppToken` - X-WhatsAppW-Token认证（严格限制）
  - 仅允许访问预定义的安全操作
  - 专门用于web页面的受限访问
  - 包含操作白名单验证

- `allowInternalCall` - 内部调用权限
  - 通过customerId参数标识内部调用
  - 绕过所有HTTP权限检查
  - 用于系统内部的直接调用

#### 角色权限规则
- `isAdmin` - 管理员权限验证
- `isRestaurantUser` - 餐厅用户权限验证
- `isCustomerUser` - 客户用户权限验证

### 资源级规则

- `isResourceOwner` - 通用资源所有权检查
- `isOrderOwner` - 订单所有权检查
- `isSelfManagement` - 自我管理权限
- `isSensitiveConfigurationAccess` - 敏感配置访问权限
- `isUserManager` - 用户管理权限

## 权限映射

### Query权限

#### 公开查询（无需认证）
```graphql
# 餐厅相关
restaurants          # 获取餐厅列表
restaurant           # 获取餐厅详情

# 菜单相关
categories           # 获取分类列表
foods               # 获取食物列表
foodByCategory      # 按分类获取食物
```

#### X-WhatsAppW-Token专用查询（严格受限）
```graphql
getCustomerAddresses  # 获取客户地址
# 注意：这是唯一允许X-WhatsAppW-Token访问的查询操作
# 用于web页面的受限访问，严格限制操作范围
```

#### 内部调用专用查询（系统内部使用）
```graphql
getRestaurantMenuForCustomer  # 获取餐厅菜单（通过customerId参数）
getCustomerOrderHistory      # 获取客户订单历史（通过customerId参数）
# 注意：这些操作只能通过内部调用访问，不能通过HTTP请求
# 用于WhatsApp系统的服务器内部直接调用
```

#### 客户查询
- `profile` - 用户配置文件
- `userFavourite` - 用户收藏

#### 餐厅查询
- `orders` - 订单列表（需要资源所有权检查）
- `order` - 订单详情（需要资源所有权检查）
- `getDashboardTotal` - 仪表板数据（需要资源所有权检查）

#### 管理员专用查询
- `users` - 用户列表
- `configuration` - 系统配置
- `allUsers` - 所有用户

### 🔄 Mutation权限分类

#### 公开变更（无需认证）
```graphql
createUser           # 用户注册
login               # 用户登录
```

#### X-WhatsAppW-Token专用变更（严格受限）
```graphql
placeOrderFromWhatsApp  # 提交订单
# 注意：这是唯一允许X-WhatsAppW-Token访问的变更操作
# 专门用于web页面的订单提交，严格限制操作范围
```

#### 客户变更
- `updateUser` - 更新用户信息（需要自我管理检查）
- `placeOrder` - 下单

#### 餐厅变更
- `updateOrderStatus` - 更新订单状态（需要资源所有权检查）
- `refundOrder` - 退款（需要资源所有权检查）
- `createRestaurant` - 创建餐厅
- `editRestaurant` - 编辑餐厅（需要资源所有权检查）

#### 管理员专用变更
- `Deactivate` - 停用用户
- `saveConfiguration` - 保存系统配置
- `save*Configuration` - 各种配置保存操作
- `updateCommission` - 更新佣金

## 安全特性

### 1. 环境隔离
- **introspection**: 仅在 `local` 环境启用
- **playground**: 仅在 `local` 环境启用
- **debug**: 仅在非生产环境启用

### 2. 默认拒绝策略
- `fallbackRule: deny` - 所有未明确允许的操作都被拒绝
- 白名单方式的权限控制

### 3. 认证机制隔离
- X-WhatsAppW-Token认证权限受限，只能访问特定操作
- 内部调用（通过customerId参数）可以访问完整的WhatsApp功能
- 在单一认证规则中实现操作级别的权限控制

### 4. 资源级权限控制
- 餐厅用户只能访问自己的餐厅资源
- 客户只能访问自己的订单和个人信息
- 基于数据库查询的所有权验证

### 5. 审计日志
- 记录所有权限检查失败的尝试
- 记录用户认证成功和失败
- 详细的安全事件日志

## 使用示例

### X-WhatsAppW-Token用户查询地址（受限操作）
```graphql
# 请求头: X-WhatsAppW-Token: <token>
query GetCustomerAddresses {
  getCustomerAddresses {
    _id
    formattedAddress
    coordinates {
      latitude
      longitude
    }
  }
}
```

### 内部调用查询菜单（完整权限）
```graphql
query GetRestaurantMenu($restaurantId: ID!, $customerId: String!) {
  getRestaurantMenuForCustomer(
    restaurantId: $restaurantId,
    customerId: $customerId
  ) {
    _id
    name
    foods {
      _id
      title
      price
    }
  }
}
```

### 管理员查询系统配置
```graphql
# 请求头: Authorization: Bearer <jwt_token>
query GetConfiguration {
  configuration {
    _id
    currency
    deliveryRate
  }
}
```

## 测试

权限控制系统包含全面的测试用例：

- **单元测试**: `test/unit/graphql/permissions.test.js`
- **认证机制测试**: 测试不同认证类型的权限控制
- **安全测试**: 测试权限绕过和攻击场景

### 运行测试

```bash
# 运行权限测试
npm test test/unit/graphql/permissions.test.js

# 运行所有GraphQL测试
npm test test/unit/graphql/
```

## 维护和扩展

### 添加新的权限规则

1. 在 `graphql/permissions/rules.js` 中定义新规则
2. 在 `graphql/permissions.js` 中应用规则
3. 添加相应的测试用例

### 添加新的认证机制

1. 在 `graphql/permissions/constants.js` 中定义新的认证类型
2. 创建相应的认证中间件
3. 更新权限规则以支持新认证机制

### 安全最佳实践

1. **最小权限原则** - 只给用户必要的最小权限
2. **认证机制隔离** - 严格区分不同认证机制的使用场景
3. **定期审计** - 定期检查权限配置和日志
4. **测试覆盖** - 确保所有权限路径都有测试覆盖
5. **监控告警** - 监控异常的权限访问尝试

## 故障排除

### 常见问题

1. **权限被拒绝** - 检查用户角色和权限映射
2. **认证机制混淆** - 确认使用正确的认证类型
3. **资源访问失败** - 检查资源所有权规则
4. **认证失败** - 检查token和认证中间件

### 调试技巧

1. 启用debug模式查看详细权限检查日志
2. 检查GraphQL context中的认证信息
3. 验证权限规则的逻辑和参数
4. 确认使用正确的认证机制

## 🔧 安全配置

### 环境配置
```javascript
// app.js - Apollo Server配置
{
  introspection: config.NODE_ENV === 'local',  // 仅本地环境启用内省
  playground: config.NODE_ENV === 'local',     // 仅本地环境启用playground
}
```

### 权限常量配置
```javascript
// graphql/permissions/constants.js
const WHATSAPP_TOKEN_ALLOWED_OPERATIONS = [
  'getCustomerAddresses',      // 查询客户地址
  'placeOrderFromWhatsApp'     // 提交订单
];
```

### 安全检查清单
- [ ] 生产环境禁用introspection和playground
- [ ] X-WhatsAppW-Token仅限制在预定义操作
- [ ] 内部调用通过customerId参数验证
- [ ] 所有敏感操作需要适当的权限验证
- [ ] 定期审查权限映射和规则

## 📋 测试验证

### 权限测试覆盖
1. **基础认证测试** - JWT token验证
2. **X-WhatsAppW-Token测试** - 受限操作验证
3. **内部调用测试** - customerId参数验证
4. **角色权限测试** - 管理员、餐厅、客户权限
5. **拒绝访问测试** - 未授权操作拒绝

### 安全测试场景
- 无效token访问尝试
- 跨权限操作尝试
- 权限提升攻击测试
- 内省和playground访问控制

## 📝 更新日志

- **v2.0.0** - 重新设计权限架构，简化认证机制，明确区分访问场景
- **v1.1.0** - 区分Web WhatsApp和WhatsApp系统认证
- **v1.0.0** - 初始权限控制系统实现
