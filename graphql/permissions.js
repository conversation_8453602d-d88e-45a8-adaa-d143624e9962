const { shield, and, deny } = require('graphql-shield');
const {
  isAuthenticated,
  isWebWhatsAppToken,
  allowInternalCall,
  isAdmin,
  isRestaurantUser,
  isCustomerUser,
  isResourceOwner,
  isOrderOwner,
  isSensitiveConfigurationAccess,
  isUserManager,
  isSelfManagement
} = require('./permissions/rules');
// 常量已在规则中使用，这里暂时注释
// const {
//   SENSITIVE_OPERATIONS,
//   WHATSAPP_OPERATIONS,
//   PUBLIC_OPERATIONS
// } = require('./permissions/constants');

// 完整权限映射
const permissions = shield({
  Query: {
    // === 公开查询 (无需认证) ===
    restaurants: true,
    restaurant: true,
    restaurantPreview: true,
    restaurantsPreview: true,
    categories: true,
    foods: true,
    foodByCategory: true,

    // === X-WhatsAppW-Token专用查询（受限访问）===
    getCustomerAddresses: isWebWhatsAppToken,

    // === 内部调用专用查询（系统内部使用）===
    getRestaurantMenuForCustomer: allowInternalCall,
    getCustomerOrderHistory: allowInternalCall,

    // === 客户查询 ===
    profile: and(isAuthenticated, isCustomerUser),
    userFavourite: and(isAuthenticated, isCustomerUser),

    // === 餐厅查询 ===
    orders: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    order: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    restaurantOrders: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    getRefund: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    getOrderRefunds: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    getDashboardTotal: and(isAuthenticated, isRestaurantUser, isResourceOwner),

    // === 管理员专用查询 ===
    users: isUserManager,
    configuration: isSensitiveConfigurationAccess,
    allUsers: isUserManager,

    // === 其他需要认证的查询 ===
    likedFood: isAuthenticated,
    orderPaypal: and(isAuthenticated, isOrderOwner),
    orderStripe: and(isAuthenticated, isOrderOwner),
  },

  Mutation: {
    // === 公开变更 (注册等) ===
    createUser: true,
    login: true,

    // === X-WhatsAppW-Token专用变更（受限访问）===
    placeOrderFromWhatsApp: isWebWhatsAppToken,

    // === 客户变更 ===
    updateUser: and(isAuthenticated, isSelfManagement),
    placeOrder: and(isAuthenticated, isCustomerUser),

    // === 餐厅变更 ===
    updateOrderStatus: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    refundOrder: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    createRestaurant: and(isAuthenticated, isRestaurantUser),
    editRestaurant: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    deleteRestaurant: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    createFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    editFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    deleteFood: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    createCategory: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    editCategory: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    deleteCategory: and(isAuthenticated, isRestaurantUser, isResourceOwner),
    toggleAvailability: and(isAuthenticated, isRestaurantUser),
    saveRestaurantToken: and(isAuthenticated, isRestaurantUser),

    // === 管理员专用变更 ===
    Deactivate: isUserManager,
    saveConfiguration: isSensitiveConfigurationAccess,
    saveEmailConfiguration: isSensitiveConfigurationAccess,
    savePaypalConfiguration: isSensitiveConfigurationAccess,
    saveStripeConfiguration: isSensitiveConfigurationAccess,
    saveTwilioConfiguration: isSensitiveConfigurationAccess,
    saveFirebaseConfiguration: isSensitiveConfigurationAccess,
    saveSentryConfiguration: isSensitiveConfigurationAccess,
    saveGoogleApiKeyConfiguration: isSensitiveConfigurationAccess,
    saveCloudinaryConfiguration: isSensitiveConfigurationAccess,
    saveAmplitudeConfiguration: isSensitiveConfigurationAccess,
    saveGoogleClientIDConfiguration: isSensitiveConfigurationAccess,
    saveWebConfiguration: isSensitiveConfigurationAccess,
    saveAppConfigurations: isSensitiveConfigurationAccess,
    saveDemoConfiguration: isSensitiveConfigurationAccess,
    updateCommission: isAdmin,

    // === 其他需要认证的变更 ===
    editOrder: and(isAuthenticated, isOrderOwner),
  }
}, {
  fallbackRule: deny, // 默认拒绝所有未明确允许的访问
  allowExternalErrors: true, // 允许将规则中的Error消息返回给客户端
  debug: process.env.NODE_ENV !== 'production' // 在非生产环境开启调试信息
});

module.exports = permissions;
